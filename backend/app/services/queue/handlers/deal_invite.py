"""
Deal Invite Email Handler

Handles sending warm invite emails to startups for deal applications.
Implements PRD 1: Deal Creation with Warm Email Invite.
"""

from pathlib import Path
from typing import Any, Dict

from app.core.config import settings
from app.core.logging import get_logger
from app.models.form import Form
from app.models.organization import Organization
from app.models.sharing import SharingConfig, SharingLink
from app.services.factory import (
    get_deal_service,
    get_email_service,
    get_sharing_service,
)

logger = get_logger(__name__)


async def send_deal_invite_email(payload: Dict[str, Any]) -> Dict[str, Any]:
    """
    Send a warm invite email to a startup for deal application.

    Args:
        payload: Job payload containing deal and invite information

    Returns:
        Processing result
    """
    try:
        # Extract payload data
        deal_id = payload.get("deal_id")
        org_id = payload.get("org_id")
        form_id = payload.get("form_id")
        company_name = payload.get("company_name")
        invited_email = payload.get("invited_email")
        company_website = payload.get("company_website")
        pitch_deck_url = payload.get("pitch_deck_url")

        if not all([deal_id, org_id, form_id, company_name, invited_email]):
            raise ValueError("Missing required fields in payload")

        logger.info(f"Processing invite email for deal {deal_id} to {invited_email}")

        # Get services
        email_service = await get_email_service()
        sharing_service = await get_sharing_service()
        deal_service = await get_deal_service()

        # Get organization details
        org = await Organization.find_one(query={"_id": org_id})
        if not org:
            raise ValueError(f"Organization not found: {org_id}")

        # Get form details
        form = await Form.find_one(query={"_id": form_id})
        if not form:
            raise ValueError(f"Form not found: {form_id}")

        # Get or create sharing link for the form
        form_link = await _get_or_create_form_share_link(
            sharing_service, str(form_id), str(org_id)
        )

        # Prepare email template variables
        template_vars = {
            "org_name": org.name,
            "company_name": company_name,
            "form_name": form.name,
            "form_link": form_link,
            "company_website": company_website,
            "pitch_deck_attached": bool(pitch_deck_url),
            # "tracking_pixel_url": f"{settings.API_V1_STR}/tracking/email-open/{deal_id}",
        }

        # Load email template
        template_path = (
            Path(__file__).parent.parent.parent
            / "templates"
            / "emails"
            / "deal_invite.html"
        )

        with open(template_path, "r", encoding="utf-8") as f:
            html_template = f.read()

        # Replace template variables
        html_content = html_template
        for key, value in template_vars.items():
            placeholder = "{{ " + key + " }}"
            html_content = html_content.replace(
                placeholder, str(value) if value else ""
            )

        # Handle conditional content
        if template_vars["pitch_deck_attached"]:
            html_content = html_content.replace("{% if pitch_deck_attached %}", "")
            html_content = html_content.replace("{% endif %}", "")
        else:
            # Remove the pitch deck section
            start_marker = "{% if pitch_deck_attached %}"
            end_marker = "{% endif %}"
            start_idx = html_content.find(start_marker)
            end_idx = html_content.find(end_marker)
            if start_idx != -1 and end_idx != -1:
                html_content = (
                    html_content[:start_idx] + html_content[end_idx + len(end_marker) :]
                )

        # Prepare email subject
        subject = f"Investment Opportunity - {org.name}"

        # Send email
        await email_service._send_email(
            to=str(invited_email), subject=subject, html_content=html_content
        )

        # Update deal invite status
        await deal_service.update_invite_status(deal_id, "sent")  # type: ignore

        logger.info(
            f"Successfully sent invite email for deal {deal_id} to {invited_email}"
        )

        return {
            "success": True,
            "deal_id": deal_id,
            "invited_email": invited_email,
            "form_link": form_link,
        }

    except Exception as e:
        logger.error(f"Error sending invite email: {str(e)}")

        # Update deal status to failed
        try:
            deal_service = await get_deal_service()
            await deal_service.update_invite_status(payload.get("deal_id"), "failed")  # type: ignore
        except:
            pass

        return {
            "success": False,
            "error": str(e),
            "deal_id": payload.get("deal_id"),
        }


async def _get_or_create_form_share_link(
    sharing_service, form_id: str, org_id: str
) -> str:
    """Get or create a sharing link for the form."""
    try:
        # Check if sharing link already exists
        sharing_config = await SharingConfig.find_one(
            query={
                "resource_type": "form",
                "resource_id": form_id,
                "org_id": org_id,
                "enabled": True,
            }
        )

        if sharing_config:
            # Get the sharing link
            sharing_link = await SharingLink.find_one(
                query={"sharing_config_id": sharing_config.id}
            )
            if sharing_link:
                return f"{settings.FRONTEND_URL}/share/{sharing_link.token}"

        # Create new sharing configuration
        sharing_data = await sharing_service.create_sharing_config(
            resource_type="form",
            resource_id=form_id,
            org_id=org_id,
            enabled=True,
            sharing_types=["link"],
            expires_at=None,  # No expiration for deal invites
        )

        if sharing_data and sharing_data.get("links"):
            return sharing_data["links"][0]["url"]

        raise Exception("Failed to create sharing link")

    except Exception as e:
        logger.error(f"Error creating form share link: {str(e)}")
        # Fallback to direct form URL
        return f"{settings.FRONTEND_URL}/forms/{form_id}"


# Register handlers
HANDLERS = {
    "send_deal_invite_email": send_deal_invite_email,
}
